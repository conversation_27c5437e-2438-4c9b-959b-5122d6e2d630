import argparse
from pathlib import Path 
import pandas as pd 
import numpy as np 
#import mlflow 
import os
from typing import Tuple, Dict
from datetime import timedelta
import logging
import yaml
import sys


# Configure logging
def setup_logging():
    """Configure logging to output to stdout with timestamp"""
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s', 
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    return logger


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser("prep")
    parser.add_argument("--input_data",type=str,required=True,help="Path to raw data")
    parser.add_argument("--train_data",type=str,required=True,help="Path to train data")
    parser.add_argument("--test_data",type=str,required=True,help="Path to test data")
    args = parser.parse_args()

    return args

def create_rolling_features(df: pd.DataFrame, target_column: str, windows: list) -> pd.DataFrame:
    """Create rolling window features for the target column"""
    df = df.copy()
    try:
        for window in windows:
            df[f'mv_{window}'] = (
                df[target_column]
                .shift(1)
                .rolling(
                    window=window,
                    min_periods=5,
                    center=False
                )
                .mean()
            )
            
            df[f'mv_{window}'] = df[f'mv_{window}'].fillna(method='ffill').fillna(method='bfill')
    except Exception as e:
        print(f"Error creating rolling features: {str(e)}")
        logger.warning(f"Error creating rolling features with standard method: {str(e)}")
        logger.info("Attempting fallback method...")
        try:
            for window in windows:
                df[f'mv_{window}'] = (
                    df[target_column]
                    .shift(1)
                    .rolling(window=window, min_periods=1)
                    .mean()
                    .fillna(method='ffill')
                    .fillna(method='bfill')
                )
        except Exception as e:
            logger.error(f"Failed to create rolling features: {str(e)}")
            raise
    
    return df



def prepare_data(df: pd.DataFrame, target_column: str, n_lags: int = 50) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Prepare data by creating features and splitting into train/test sets"""
    try:
        # Convert to wide format
        logger.info("Converting data to wide format...")
    # Convert to wide format
        df_narrow = df.loc[df['TagValue'].notnull()]
        df_wide = df_narrow.pivot_table(
            index=['EventDate'],
            columns=['TagName'],
            values='TagValue'
        ).reset_index()
        print(f"Processing Tag {target_column} ....")
        # print("With noise shape is: ",df_wide.shape)
        # Remove outliers
        logger.info("Removing outliers...")
        Q1 = df_wide[target_column].quantile(0.25)
        Q3 = df_wide[target_column].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        df_cleaned = df_wide[(df_wide[target_column] >= lower_bound) & 
                            (df_wide[target_column] <= upper_bound)]
        # Create features
        logger.info(f"Shape after cleaning: {df_cleaned.shape}")
        # 1. Lag features
        # print("Without noise Shape is ",df_cleaned.shape)
        logger.info(f"Creating {n_lags} lag features...")
        for i in range(n_lags):
            df_cleaned[f'Lag-{i+1}'] = df_cleaned[target_column].shift(i+1)

        logger.info("Creating date features...")
        # 2. Date features
        df_cleaned['day_of_month'] = df_cleaned.EventDate.dt.day
        df_cleaned['day_of_year'] = df_cleaned.EventDate.dt.dayofyear
        df_cleaned['day_of_week'] = df_cleaned.EventDate.dt.dayofweek + 1
        df_cleaned['hour_of_day'] = df_cleaned.EventDate.dt.hour
        df_cleaned['weekday'] = df_cleaned.EventDate.dt.weekday

        df_encoded = pd.get_dummies(
            df_cleaned, 
            columns=['weekday', 'day_of_month', 'day_of_week', 'hour_of_day']
        )
        # 3. Window features
        windows = [6, 9, 12, 15, 18, 21, 24]
        df_encoded = create_rolling_features(df_encoded, target_column, windows)


        logger.info("Splitting into train and test sets...")
        total_days = (df_encoded['EventDate'].max() - df_encoded['EventDate'].min()).days
        test_days = total_days * 0.2 
        cut_date = df_encoded['EventDate'].max() - timedelta(days=test_days)
        
        train = df_encoded[df_encoded['EventDate'] <= cut_date].copy()
        test = df_encoded[df_encoded['EventDate'] > cut_date].copy()
        
    except Exception as e:
        logger.error(f"Error in data preparation: {str(e)}")
        raise

    return train, test
    
    

def main():
    """Main execution function"""
    args = parse_args()
    try:
        
        data = pd.read_csv(Path(args.input_data)) 
        #/"finaldata.csv"
        data['EventDate'] = pd.to_datetime(data['EventDate'], errors='coerce')
        data['Header_timestamp'] = pd.to_datetime(data['Header_timestamp'], errors='coerce')    
        
        # Configuration for different tags
        tags_config = {
            'humidity': {
                'target': "Kitting_Humidity",
                'output_prefix': "humidity"
            },
            'temperature': {
                'target': "Kitting_Temperature",
                'output_prefix': "temperature"
            },
            'pressure': {
                'target': "Kitting_Pressure",
                'output_prefix': "pressure"
            }
        }

        for tag, config in tags_config.items():
            if config['target'] in data['TagName'].unique():
                #print(f"\nProcessing {tag} data...")
                
                # Filter data for current tag
                tag_data = data[data['TagName'] == config['target']].copy()
                
                # Prepare train/test splits
                train_df, test_df = prepare_data(
                    df=tag_data,
                    target_column=config['target']
                )
                
                # Save processed data
                train_path = Path(args.train_data)
                test_path = Path(args.test_data)

                train_df.to_csv(train_path / f"{config['output_prefix']}_train.csv", index=False)
                test_df.to_csv(test_path / f"{config['output_prefix']}_test.csv", index=False)
                
                print(f"Processed {tag} data - Training shape: {train_df.shape}")

    except Exception as ex:
        logger.error(f"Pipeline failed: {str(ex)}")
        raise

if __name__ == "__main__":
    """Calling logger and main function from here"""
    logger = setup_logging()
    main()

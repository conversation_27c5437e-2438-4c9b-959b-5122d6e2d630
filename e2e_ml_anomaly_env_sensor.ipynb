{"cells": [{"cell_type": "code", "source": ["from azure.identity import DefaultAzureCredential,InteractiveBrowserCredential\n", "from azure.ai.ml import MLClient\n", "\n", "try:\n", "    credential = DefaultAzureCredential()\n", "    credential.get_token(\"https://management.azure.com/.default\")\n", "except Exception as ex:\n", "    credential = InteractiveBrowserCredential()"], "outputs": [], "execution_count": 1, "metadata": {"gather": {"logged": 1748952133268}}}, {"cell_type": "code", "source": ["ml_client=MLClient.from_config(credential=credential)"], "outputs": [{"output_type": "stream", "name": "stderr", "text": "Found the config file in: /config.json\n"}], "execution_count": 2, "metadata": {"gather": {"logged": 1748952136964}}}, {"cell_type": "markdown", "source": ["**_using the Azure Machine Learning SDK to register a local data file as a data asset in Azure ML_**"], "metadata": {"nteract": {"transient": {"deleting": false}}}}, {"cell_type": "code", "source": ["from azure.ai.ml.entities import Data\n", "from azure.ai.ml.constants import AssetTypes\n", "\n", "# Define the local path to your data file\n", "local_path = \"Data/Kittitng_room/raw/finaldata.csv\"\n", "\n", "data = Data(\n", "    name=\"kitting_final_data\",\n", "    path=local_path,\n", "    type=AssetTypes.URI_FILE,\n", "    description=\"Dataset for Environmental Sensors\",\n", "    tags={\"source_type\": \"file\"},\n", "    version=\"2\",\n", ")"], "outputs": [], "execution_count": 3, "metadata": {"gather": {"logged": 1748262210658}}}, {"cell_type": "code", "source": ["data = ml_client.data.create_or_update(data)\n", "print(\n", "    f\"Dataset with name {data.name} was registered to workspace, the dataset version is {data.version}\"\n", ")"], "outputs": [{"output_type": "stream", "name": "stdout", "text": "Dataset with name kitting_final_data was registered to workspace, the dataset version is 2\n"}], "execution_count": 4, "metadata": {"gather": {"logged": 1748262218116}}}, {"cell_type": "code", "source": ["from azure.ai.ml import Input\n", "pipeline_input_data=Input(type=\"uri_file\", path=data.path)\n", "pipeline_input_data"], "outputs": [{"output_type": "execute_result", "execution_count": 8, "data": {"text/plain": "{'type': 'uri_file', 'path': 'azureml://subscriptions/111bc7bc-dc50-49e7-bc8b-17b1b9f72294/resourcegroups/rg-nerve-center-dev-eastus2/workspaces/nerve-center-aml-dev2/datastores/workspaceblobstore/paths/LocalUpload/f2348713cebfb8db487fb90f59e8a0a0/finaldata.csv'}"}, "metadata": {}}], "execution_count": 8, "metadata": {"gather": {"logged": 1747729786255}}}, {"cell_type": "code", "source": ["from azure.ai.ml import Input, Output, command\n", "\n", "# def create_data_prep_component(ml_client):\n", "data_prep_component = command(\n", "    name=\"data_preparation_kitting_room\",\n", "    display_name=\"Data Preparation for training\",\n", "    description=\"Prepare sensor data for training\",\n", "    inputs={\n", "        \"input_data\": Input(type=\"uri_file\", description=\"Input raw sensor data\"),\n", "    },\n", "    outputs={\n", "        \"train_data\": Output(type=\"uri_folder\", description=\"Prepared training data\"),\n", "        \"test_data\": Output(type=\"uri_folder\", description=\"Prepared test data\")\n", "    },\n", "    code=\"./DataScience/src\",\n", "    command=\"\"\"python prep.py \\\n", "            --input_data ${{inputs.input_data}} \\\n", "            --train_data ${{outputs.train_data}} \\\n", "            --test_data ${{outputs.test_data}}\n", "            \"\"\",\n", "    environment=\"aml-scikit-learn-lightgbm:10\"\n", ")\n", "    \n", "    # return ml_client.components.create_or_update(data_prep_component)"], "outputs": [], "execution_count": 3, "metadata": {"gather": {"logged": 1748952142642}}}, {"cell_type": "code", "source": ["# Now we register the component to the workspace\n", "data_prep_component = ml_client.create_or_update(data_prep_component.component)\n", "\n", "# Create (register) the component in your workspace\n", "print(\n", "    f\"Component {data_prep_component.name} with Version {data_prep_component.version} is registered\"\n", ")"], "outputs": [{"output_type": "stream", "name": "stderr", "text": "\r\u001b[32mUploading src (0.03 MBs):   0%|          | 0/34137 [00:00<?, ?it/s]\r\u001b[32mUploading src (0.03 MBs): 100%|██████████| 34137/34137 [00:00<00:00, 826016.97it/s]\n\u001b[39m\n\n"}, {"output_type": "stream", "name": "stdout", "text": "Component data_preparation_kitting_room with Version 2025-06-03-12-02-33-4736228 is registered\n"}], "execution_count": 4, "metadata": {"gather": {"logged": 1748952155255}}}, {"cell_type": "code", "source": ["from azure.ai.ml import Input, Output, command\n", "\n", "\n", "train_component = command(\n", "    name=\"model_training_kitting_room\",\n", "    display_name=\"Model Training for Kitting Room\",\n", "    description=\"Train sensor models using LightGBM\",\n", "    inputs={\n", "        \"train_data_path\": Input(type=\"uri_folder\", description=\"Training data directory\"),\n", "        \"test_data_path\": Input(type=\"uri_folder\", description=\"Test data directory\"),\n", "        \"num_leaves\": Input(type = \"number\", description=\"first hyperparameter for algorithm\"),\n", "        \"learning_rate\": Input(type = \"number\", description=\"second hyperparameter for algorithm\"),\n", "        \"feature_fraction\": Input(type = \"number\", description=\"third hyperparameter for algorithm\"),\n", "        \"max_depth\": Input(type = \"number\", description=\"fourth hyperparameter for algorithm\"),\n", "        \"num_boost_round\": Input(type = \"number\", description=\"fifth hyperparameter for algorithm\"),\n", "        \"early_stopping_rounds\": Input(type = \"number\", description=\"sixth hyperparameter for algorithm\"),\n", "        \"nthread\": Input(type = \"number\", description=\"seventh hyperparameter for algorithm\")\n", "\n", "    },\n", "    outputs={\n", "        \"model_output\": Output(type=\"uri_folder\", description=\"Trained model directory\")\n", "    },\n", "    code=\"./DataScience/src\",\n", "    command=\"\"\"python train.py \\\n", "            --train_data_path ${{inputs.train_data_path}} \\\n", "            --test_data_path ${{inputs.test_data_path}} \\\n", "            --num_leaves ${{inputs.num_leaves}}\\\n", "            --learning_rate ${{inputs.learning_rate}}\\\n", "            --feature_fraction ${{inputs.feature_fraction}} \\\n", "            --max_depth ${{inputs.max_depth}} \\\n", "            --num_boost_round ${{inputs.num_boost_round}}\\\n", "            --early_stopping_rounds ${{inputs.early_stopping_rounds}} \\\n", "            --nthread ${{inputs.nthread}}\\\n", "            --model_output ${{outputs.model_output}}\n", "            \"\"\",\n", "    environment=\"aml-scikit-learn-lightgbm:10\"\n", ")"], "outputs": [], "execution_count": 5, "metadata": {"gather": {"logged": 1748952155402}}}, {"cell_type": "code", "source": ["# Now we register the component to the workspace\n", "data_train_component = ml_client.create_or_update(train_component.component)\n", "\n", "# Create (register) the component in your workspace\n", "print(\n", "    f\"Component {data_train_component.name} with Version {data_train_component.version} is registered\"\n", ")"], "outputs": [{"output_type": "stream", "name": "stdout", "text": "Component model_training_kitting_room with Version 2025-06-03-12-02-37-3349431 is registered\n"}], "execution_count": 6, "metadata": {"gather": {"logged": 1748952159226}}}, {"cell_type": "code", "source": ["from azure.ai.ml import Input, command\n", "\n", "\n", "register_component = command(\n", "    name=\"model_registration_kitting_room\",\n", "    display_name=\"Model Registration for kitting room\",\n", "    description=\"Register trained models in MLflow\",\n", "    inputs={\n", "        \"model_path\": Input(type=\"uri_folder\", description=\"Path to trained models\"),\n", "        \"model_name\": Input(type=\"string\", description=\"Base name for registered models\")\n", "    },\n", "    code=\"./DataScience/src\",\n", "    command=\"\"\"python register.py \\\n", "            --model_path ${{inputs.model_path}} \\\n", "            --model_name ${{inputs.model_name}}\n", "            \"\"\",\n", "    environment=\"aml-scikit-learn-lightgbm:10\"\n", ")"], "outputs": [], "execution_count": 12, "metadata": {"gather": {"logged": 1748953136392}}}, {"cell_type": "code", "source": ["# Now we register the component to the workspace\n", "model_register_component = ml_client.create_or_update(register_component.component)\n", "\n", "# Create (register) the component in your workspace\n", "print(\n", "    f\"Component {model_register_component.name} with Version {model_register_component.version} is registered\"\n", ")"], "outputs": [{"output_type": "stream", "name": "stderr", "text": "\r\u001b[32mUploading src (0.03 MBs):   0%|          | 0/34136 [00:00<?, ?it/s]\r\u001b[32mUploading src (0.03 MBs): 100%|██████████| 34136/34136 [00:00<00:00, 665381.36it/s]\n\u001b[39m\n\n"}, {"output_type": "stream", "name": "stdout", "text": "Component model_registration_kitting_room with Version 2025-06-03-12-19-02-3211227 is registered\n"}], "execution_count": 13, "metadata": {"gather": {"logged": 1748953143631}}}, {"cell_type": "code", "source": ["\n", "from azure.ai.ml import dsl, Input\n", "\n", "@dsl.pipeline(\n", "    compute=\"serverless\",\n", "    description=\"E2E pipeline for sensor data processing, model training, and registration in the Kitting Room\",\n", ")\n", "def kitting_room_pipeline(\n", "    pipeline_input_data,\n", "    pipeline_number_leaves,\n", "    pipeline_learning_rate,\n", "    pipeline_feature_fraction,\n", "    pipeline_max_depth,\n", "    pipeline_num_boost_round,\n", "    pipeline_early_stopping_rounds,\n", "    pipeline_nthread,\n", "    pipeline_model_base_name,\n", "                            ):\n", "\n", "    # Step 1: Data Preparation\n", "    data_prep_job = data_prep_component(\n", "         input_data=pipeline_input_data,\n", "          )\n", "\n", "    # Step 2: Model Training\n", "    train_job = train_component(\n", "        train_data_path=data_prep_job.outputs.train_data,\n", "        test_data_path=data_prep_job.outputs.test_data,\n", "        num_leaves=pipeline_number_leaves,\n", "        learning_rate=pipeline_learning_rate,\n", "        feature_fraction=pipeline_feature_fraction,\n", "        max_depth=pipeline_max_depth,\n", "        num_boost_round=pipeline_num_boost_round,\n", "        early_stopping_rounds=pipeline_early_stopping_rounds,\n", "        nthread=pipeline_nthread,\n", "     )\n", "\n", "    # Step 3: Model Registration\n", "    register_job = register_component(\n", "    model_path=train_job.outputs.model_output,\n", "     model_name=pipeline_model_base_name,\n", "    )\n", "\n", "    return {\n", "     \"prepared_train_data\": data_prep_job.outputs.train_data,\n", "    \"prepared_test_data\": data_prep_job.outputs.test_data,\n", "     }\n"], "outputs": [], "execution_count": 14, "metadata": {"gather": {"logged": 1748953148938}}}, {"cell_type": "code", "source": ["registered_model_name = \"environmental_sensor_model\"\n", "\n", "# Let's instantiate the pipeline with the parameters of our choice\n", "pipeline = kitting_room_pipeline(\n", "    pipeline_input_data=Input(type=\"uri_file\", path=\"azureml://subscriptions/111bc7bc-dc50-49e7-bc8b-17b1b9f72294/resourcegroups/rg-nerve-center-dev-eastus2/workspaces/nerve-center-aml-dev2/datastores/workspaceblobstore/paths/LocalUpload/f2348713cebfb8db487fb90f59e8a0a0/finaldata.csv\"),\n", "    pipeline_number_leaves=10,\n", "    pipeline_learning_rate=0.02,\n", "    pipeline_feature_fraction=0.8,\n", "    pipeline_max_depth=5,\n", "    pipeline_num_boost_round=15000,\n", "    pipeline_early_stopping_rounds=200,\n", "    pipeline_nthread=-1,\n", "    pipeline_model_base_name=registered_model_name\n", ")"], "outputs": [], "execution_count": 15, "metadata": {"gather": {"logged": 1748953152078}}}, {"cell_type": "code", "source": ["import webbrowser\n", "\n", "# submit the pipeline job\n", "pipeline_job = ml_client.jobs.create_or_update(\n", "    pipeline,\n", "    # Project's name\n", "    experiment_name=\"e2e_registered_pipeline_components\",\n", ")\n", "ml_client.jobs.stream(pipeline_job.name)\n", "# open the pipeline in web browser\n", "webbrowser.open(pipeline_job.studio_url)"], "outputs": [{"output_type": "stream", "name": "stderr", "text": "pathOnCompute is not a known attribute of class <class 'azure.ai.ml._restclient.v2023_04_01_preview.models._models_py3.UriFolderJobOutput'> and will be ignored\npathOnCompute is not a known attribute of class <class 'azure.ai.ml._restclient.v2023_04_01_preview.models._models_py3.UriFolderJobOutput'> and will be ignored\npathOnCompute is not a known attribute of class <class 'azure.ai.ml._restclient.v2023_04_01_preview.models._models_py3.UriFolderJobOutput'> and will be ignored\n"}, {"output_type": "stream", "name": "stdout", "text": "RunId: quirky_beach_rdkgkpg2mg\nWeb View: https://ml.azure.com/runs/quirky_beach_rdkgkpg2mg?wsid=/subscriptions/111bc7bc-dc50-49e7-bc8b-17b1b9f72294/resourcegroups/rg-nerve-center-dev-eastus2/workspaces/nerve-center-aml-dev2\n\nStreaming logs/azureml/executionlogs.txt\n========================================\n\n[2025-06-03 12:19:21Z] Completing processing run id 32786f98-d7a9-4dc4-af18-c251333deb6c.\n[2025-06-03 12:19:23Z] Completing processing run id 30c7cfb0-f18a-480e-b71f-bc59f9354087.\n[2025-06-03 12:19:24Z] Submitting 1 runs, first five are: c43bf2c2:2620492c-4afe-48a9-bd96-e3cfd0303672\n[2025-06-03 12:20:06Z] Completing processing run id 2620492c-4afe-48a9-bd96-e3cfd0303672.\n\nExecution Summary\n=================\nRunId: quirky_beach_rdkgkpg2mg\nWeb View: https://ml.azure.com/runs/quirky_beach_rdkgkpg2mg?wsid=/subscriptions/111bc7bc-dc50-49e7-bc8b-17b1b9f72294/resourcegroups/rg-nerve-center-dev-eastus2/workspaces/nerve-center-aml-dev2\n\n"}, {"output_type": "execute_result", "execution_count": 16, "data": {"text/plain": "False"}, "metadata": {}}], "execution_count": 16, "metadata": {"gather": {"logged": 1748953209069}}}], "metadata": {"kernelspec": {"name": "python310-sdkv2", "language": "python", "display_name": "Python 3.10 - SDK v2"}, "language_info": {"name": "python", "version": "3.10.14", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "microsoft": {"ms_spell_check": {"ms_spell_check_language": "en"}, "host": {"AzureML": {"notebookHasBeenCompleted": true}}}, "kernel_info": {"name": "python310-sdkv2"}, "nteract": {"version": "nteract-front-end@1.0.0"}}, "nbformat": 4, "nbformat_minor": 2}
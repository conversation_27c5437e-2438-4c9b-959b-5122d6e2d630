import pandas as pd
import numpy as np
import lightgbm as lgb
import mlflow
import os
import argparse
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import (
    mean_absolute_percentage_error, 
    mean_absolute_error, 
    r2_score, 
    mean_squared_error
)
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential


def parse_args():
    """parsing command line argument"""
    parser = argparse.ArgumentParser(description='Train LightGBM models for sensors')
    parser.add_argument('--train_data_path', type=str, default="../../Data/Kittitng_room/train",
                      help='Path to training data directory')
    parser.add_argument('--test_data_path', type=str, default="../../Data/Kittitng_room/test",
                      help='Path to test data directory')
    parser.add_argument('--model_output', type=str, default="../../Model",
                      help='Path to save models')
    parser.add_argument('--num_leaves', type=int, default=10,
                      help='Number of leaves in LightGBM model')
    parser.add_argument('--learning_rate', type=float, default=0.02,
                      help='Learning rate')
    parser.add_argument('--feature_fraction', type=float, default=0.8,
                      help='Feature fraction')
    parser.add_argument('--max_depth', type=int, default=5,
                      help='Maximum tree depth')
    parser.add_argument('--num_boost_round', type=int, default=15000,
                      help='Number of boosting rounds')
    parser.add_argument('--early_stopping_rounds', type=int, default=200,
                      help='Early stopping rounds')
    parser.add_argument('--nthread', type=int, default=-1,
                      help='Number of threads')
    
    return parser.parse_args()
    
    


def train_model(tag_name, train_df, test_df, args):
    """Train a single model for given tag WITHOUT creating separate MLflow runs."""

    print(f"Training model for {tag_name}...")

    # Prepare features
    feature_cols = [col for col in train_df.columns if col not in ['EventDate', tag_name]]
    X_train = train_df[feature_cols]
    y_train = train_df[tag_name]
    X_test = test_df[feature_cols]
    y_test = test_df[tag_name]

    # Create datasets
    lgbtrain = lgb.Dataset(data=X_train, label=y_train, feature_name=feature_cols)
    lgbtest = lgb.Dataset(data=X_test, label=y_test, reference=lgbtrain, feature_name=feature_cols)

    # Model parameters
    params = {
        'num_leaves': args.num_leaves,
        'learning_rate': args.learning_rate,
        'feature_fraction': args.feature_fraction,
        'max_depth': args.max_depth,
        'verbose': 1,
        'early_stopping_rounds': args.early_stopping_rounds,
        'nthread': args.nthread
    }

    # Train model (without creating a new MLflow run)
    model = lgb.train(
        params=params,
        train_set=lgbtrain,
        valid_sets=[lgbtrain, lgbtest],
        num_boost_round=args.num_boost_round
    )

    # Make predictions and calculate metrics
    y_pred_test = model.predict(X_test)
    metrics = {
        'MAPE': mean_absolute_percentage_error(y_test, y_pred_test),
        'MAE': mean_absolute_error(y_test, y_pred_test),
        'RMSE': np.sqrt(mean_squared_error(y_test, y_pred_test)),
        'R2': r2_score(y_test, y_pred_test)
    }

    # Save model to disk (without MLflow logging during training)
    model_path = os.path.join(args.model_output, f"{tag_name}_model_V3")
    os.makedirs(model_path, exist_ok=True)

    # Save the model using LightGBM's native save method
    model.save_model(os.path.join(model_path, "model.txt"))

    # Also save using MLflow format for compatibility
    try:
        mlflow.lightgbm.save_model(lgb_model=model, path=model_path)
    except Exception as e:
        print(f"Warning: Could not save MLflow format for {tag_name}: {e}")

    # Save the path as a text file for debugging
    path_file = os.path.join(args.model_output, f"{tag_name}_path.txt")
    with open(path_file, "w") as f:
        f.write(model_path)

    print(f"Model saved to {model_path}")
    print(f"Metrics for {tag_name}:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")

    return model, metrics, model_path
    
def main():
    # Parse arguments
    args = parse_args()

    # Create model output directory
    os.makedirs(args.model_output, exist_ok=True)

    print("Starting training process...")
    print(f"Model output directory: {args.model_output}")

    # List of sensors to process
    sensors = ['Kitting_Humidity', 'Kitting_Temperature', 'Kitting_Pressure']

    # Process each sensor
    results = {}
    for sensor in sensors:
        print(f"\n{'='*50}")
        print(f"Processing {sensor}...")
        print(f"{'='*50}")

        # Load train and test data
        train_path = os.path.join(args.train_data_path, f"{sensor.lower().split('_')[1]}_train.csv")
        test_path = os.path.join(args.test_data_path, f"{sensor.lower().split('_')[1]}_test.csv")

        print(f"Loading training data from: {train_path}")
        print(f"Loading test data from: {test_path}")

        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)

        print(f"Training data shape: {train_df.shape}")
        print(f"Test data shape: {test_df.shape}")

        # Train model
        model, metrics, model_path = train_model(sensor, train_df, test_df, args)

        results[sensor] = {
            'model': model,
            'metrics': metrics,
            'model_path': model_path
        }

    print(f"\n{'='*50}")
    print("Training completed successfully for all sensors!")
    print(f"{'='*50}")

    # Summary
    for sensor, result in results.items():
        print(f"{sensor}: Model saved to {result['model_path']}")

    print("All models trained and saved successfully.")

    # Final cleanup: ensure no stray MLflow runs are left active
    try:
        if mlflow.active_run():
            print("Cleaning up any remaining MLflow runs...")
            mlflow.end_run()
    except Exception as e:
        print(f"Note: MLflow cleanup completed with message: {e}")

if __name__ == "__main__":
    main()


    
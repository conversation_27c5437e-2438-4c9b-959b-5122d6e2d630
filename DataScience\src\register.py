import argparse
import os
import mlflow
import json
import logging
from typing import Dict, List

def setup_logging():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description='Register trained sensor models in MLflow')
    parser.add_argument('--model_path', type=str, required=True,
                      help='path to the trained models')
    parser.add_argument('--model_name', type=str, required=True,
                      help='name for the registered models')
    parser.add_argument('--experiment_name', type=str, default="model_registration",
                      help='Name of the MLflow experiment')
    return parser.parse_args()

def setup_azure_ml():
    """Setup MLflow tracking"""
    if "MLFLOW_TRACKING_URI" in os.environ:
        print(f"Using MLflow tracking URI from environment: {os.environ['MLFLOW_TRACKING_URI']}")
    else:
        # Fallback for local development
        os.makedirs("./mlruns", exist_ok=True)
        mlflow.set_tracking_uri("file:./mlruns")
        print("MLFlow tracking URI set to local directory: ./mlruns")
    
    return None
    

def register_sensor_model(
    sensor_name: str,
    model_path: str,
    model_base_name: str,
    experiment_name: str,
    logger: logging.Logger
) -> Dict:
    """Register a single sensor model in MLflow"""
    try:
        # Ensure no active run exists before starting a new one
        if mlflow.active_run():
            mlflow.end_run()

        # Set the experiment
        mlflow.set_experiment(experiment_name)

        # Construct model name with sensor type
        model_name = f"{model_base_name}_{sensor_name}"

        # Load the model
        full_model_path = os.path.join(model_path, f"{sensor_name}_model_V3")
        logger.info(f"Attempting to load model from: {full_model_path}")

        # List directory contents to debug
        if os.path.exists(model_path):
            logger.info(f"Contents of model directory: {os.listdir(model_path)}")
        else:
            logger.warning(f"Model path does not exist: {model_path}")

        model = mlflow.lightgbm.load_model(full_model_path)

        # Start MLflow run for model registration
        with mlflow.start_run(run_name=f"register_{sensor_name}") as run:
            # Log the model tags
            mlflow.set_tags({
                "sensor_type": sensor_name,
                "model_version": "V3",
                "model_type": "lightgbm"
            })
            try:
                mlflow.lightgbm.log_model(
                    lgb_model=model,
                    artifact_path="model",
                    registered_model_name=model_name
                )
            except Exception as reg_error:
                logger.warning(f"Model registration failed, falling back to logging only : {str(reg_error)}")
                #fallback to just logging the model without registration
                mlflow.lightgbm.log_model(
                    lgb_model = model,
                    artifact_path="model"
                )
            
            # Get the run ID
            run_id = run.info.run_id
            
        logger.info(f"Model registered successfully for {sensor_name} with name: {model_name}")
        logger.info(f"MLflow Run ID: {run_id}")
        
        return {
            "sensor_name": sensor_name,
            "model_name": model_name,
            "run_id": run_id,
             "experiment": experiment_name,
            "status": "registered"
        }
        
    except Exception as e:
        logger.error(f"Failed to register model for {sensor_name}: {str(e)}")
        return {
            "sensor_name": sensor_name,
            "status": "failed",
            "error": str(e)
        }

def register_all_models(model_path: str, model_base_name: str,experiment_name: str, logger: logging.Logger):
    """Register all sensor models"""
    sensors = ['Kitting_Humidity', 'Kitting_Temperature', 'Kitting_Pressure']
    registration_results = []
    
    for sensor in sensors:
        logger.info(f"Registering model for {sensor}...")
        result = register_sensor_model(
            sensor_name=sensor,
            model_path=model_path,
            model_base_name=model_base_name,
            logger=logger,
            experiment_name=experiment_name
        )
        registration_results.append(result)
    
    return registration_results

def main():
    logger = setup_logging()
    args = parse_args()
    
    logger.info("Setting up MLflow tracking...")
    mlclient = setup_azure_ml()
    
    # Log environment variables to help debug
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Model path argument: {args.model_path}")

    # Ensure model path exists
    model_path = args.model_path
    if not os.path.exists(model_path):
        logger.warning(f"Model path does not exist: {model_path}")
        # Try to list parent directory
        parent_dir = os.path.dirname(model_path)
        if os.path.exists(parent_dir):
            logger.info(f"Contents of parent directory: {os.listdir(parent_dir)}")
    else:
        logger.info(f"Model path exists. Contents: {os.listdir(model_path)}")

    # if mlclient is None:
    #     logger.warning("Azure ML client setup failed, continuing with local MLFLOW tracking")
        
    logger.info(f"Registering models from path: {args.model_path}")
    registration_results = register_all_models(args.model_path, args.model_name, args.experiment_name,logger)
    
    # Summary
    successful = sum(1 for r in registration_results if r["status"] == "registered")
    logger.info(f"\nRegistration Summary:")
    logger.info(f"Total models: {len(registration_results)}")
    logger.info(f"Successfully registered: {successful}")
    logger.info(f"Failed: {len(registration_results) - successful}")


if __name__ == "__main__":
    main()
# python register.py --model_path ../../Model --model_name sensor_model --experiment_name test_exp
import pandas as pd
import numpy as np
import lightgbm as lgb
import mlflow
import os
import argparse
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import (
    mean_absolute_percentage_error, 
    mean_absolute_error, 
    r2_score, 
    mean_squared_error
)
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential


def parse_args():
    """parsing command line argument"""
    parser = argparse.ArgumentParser(description='Train LightGBM models for sensors')
    parser.add_argument('--train_data_path', type=str, default="../../Data/Kittitng_room/train",
                      help='Path to training data directory')
    parser.add_argument('--test_data_path', type=str, default="../../Data/Kittitng_room/test",
                      help='Path to test data directory')
    parser.add_argument('--model_output', type=str, default="../../Model",
                      help='Path to save models')
    parser.add_argument('--num_leaves', type=int, default=10,
                      help='Number of leaves in LightGBM model')
    parser.add_argument('--learning_rate', type=float, default=0.02,
                      help='Learning rate')
    parser.add_argument('--feature_fraction', type=float, default=0.8,
                      help='Feature fraction')
    parser.add_argument('--max_depth', type=int, default=5,
                      help='Maximum tree depth')
    parser.add_argument('--num_boost_round', type=int, default=15000,
                      help='Number of boosting rounds')
    parser.add_argument('--early_stopping_rounds', type=int, default=200,
                      help='Early stopping rounds')
    parser.add_argument('--nthread', type=int, default=-1,
                      help='Number of threads')
    
    return parser.parse_args()
    
    


def train_model(tag_name, train_df, test_df, args):
    """Train a single model for given tag with MLflow tracking."""
    
    # Set experiment for this sensor
    experiment_name = f"{tag_name}_experiment"
    #mlflow.set_experiment(experiment_name)

    # Prepare features
    feature_cols = [col for col in train_df.columns if col not in ['EventDate', tag_name]]
    X_train = train_df[feature_cols]
    y_train = train_df[tag_name]
    X_test = test_df[feature_cols]
    y_test = test_df[tag_name]

    # Create datasets
    lgbtrain = lgb.Dataset(data=X_train, label=y_train, feature_name=feature_cols)
    lgbtest = lgb.Dataset(data=X_test, label=y_test, reference=lgbtrain, feature_name=feature_cols)

    # Model parameters
    params = {
        'num_leaves': args.num_leaves,
        'learning_rate': args.learning_rate,
        'feature_fraction': args.feature_fraction,
        'max_depth': args.max_depth,
        'verbose': 1,
        'early_stopping_rounds': args.early_stopping_rounds,
        'nthread': args.nthread
    }

    with mlflow.start_run(run_name=f"{tag_name}_training"):
        # Log parameters
        mlflow.log_params(params)

        # Train model
        model = lgb.train(
            params=params,
            train_set=lgbtrain,
            valid_sets=[lgbtrain, lgbtest],
            num_boost_round=args.num_boost_round
            )

        # Make predictions and calculate metrics
        y_pred_test = model.predict(X_test)
        metrics = {
            'MAPE': mean_absolute_percentage_error(y_test, y_pred_test),
            'MAE': mean_absolute_error(y_test, y_pred_test),
            'RMSE': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'R2': r2_score(y_test, y_pred_test)
        }

        # Log metrics
        mlflow.log_metrics(metrics)

        # Save model
        model_path = os.path.join(args.model_output, f"{tag_name}_model_V3")
        os.makedirs(model_path, exist_ok=True)
        mlflow.lightgbm.save_model(lgb_model=model, path=model_path)
        
        # Also log model to MLflow for easier retrieval
        mlflow.lightgbm.log_model(lgb_model=model, artifact_path=f"{tag_name}_model")
        
        # Log the path as an artifact for debugging
        path_file = os.path.join(args.model_output, f"{tag_name}_path.txt")
        with open(path_file, "w") as f:
            f.write(model_path)
        mlflow.log_artifact(path_file)
        
        print(f"Model saved to {model_path}")

        print(f"\nMetrics for {tag_name}:")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")

    return model, metrics, model_path
    
def main():
    # Parse arguments
    args = parse_args()
    
    # Create model output directory
    os.makedirs(args.model_output, exist_ok=True)
    
    # Setup Azure ML and MLflow tracking
    #ml_client = setup_azure_ml()
    
    # List of sensors to process
    sensors = ['Kitting_Humidity', 'Kitting_Temperature', 'Kitting_Pressure']
    
    # Process each sensor
    results = {}
    for sensor in sensors:
        print(f"\nProcessing {sensor}...")
        
        # Load train and test data
        train_path = os.path.join(args.train_data_path, f"{sensor.lower().split('_')[1]}_train.csv")
        test_path = os.path.join(args.test_data_path, f"{sensor.lower().split('_')[1]}_test.csv")
        
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        
        # Train model
        model, metrics,model_path = train_model(sensor, train_df, test_df, args)
        
        results[sensor] = {
            'model': model,
            'metrics': metrics,
            'model_path': model_path
        }

if __name__ == "__main__":
    main()


    